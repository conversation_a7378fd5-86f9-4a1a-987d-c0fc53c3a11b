import { useCallback } from 'react';
import { TimelineItem, ImageFile, ExportSettings, ExportResponse } from '../types/slideshow.types';

const API_BASE_URL = window.location.hostname === 'localhost'
  ? 'http://localhost:3001'
  : window.location.origin;

// Helper function to trigger file download
const triggerDownload = (downloadUrl: string) => {
  try {
    // Create a temporary anchor element to trigger download
    const link = document.createElement('a');
    link.href = `${API_BASE_URL}${downloadUrl}`;
    link.download = ''; // Let browser determine filename from Content-Disposition header
    link.style.display = 'none';

    // Add to DOM, click, and remove
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    console.log('🔽 Download triggered for:', downloadUrl);
  } catch (error) {
    console.error('❌ Failed to trigger download:', error);
  }
};

// Función optimizada para manejar la exportación con WebSocket + fallback polling
const handleExportResponse = async (
  result: any,
  onProgress?: (progress: number, message: string) => void
): Promise<ExportResponse> => {
  // Si hay jobId y statusUrl, necesitamos hacer polling
  if (result.jobId && result.statusUrl) {
    console.log('🔄 Trabajo en cola, usando WebSocket + fallback polling:', result.jobId);

    return new Promise((resolve, reject) => {
      let isResolved = false;
      let pollInterval: number | null = null;
      let websocketTimeout: number | null = null;

      // Función para limpiar recursos
      const cleanup = () => {
        if (pollInterval) clearInterval(pollInterval);
        if (websocketTimeout) clearTimeout(websocketTimeout);
      };

      // Función para resolver una sola vez
      const resolveOnce = (data: any) => {
        if (!isResolved) {
          isResolved = true;
          cleanup();
          resolve(data);
        }
      };

      // Función para rechazar una sola vez
      const rejectOnce = (error: Error) => {
        if (!isResolved) {
          isResolved = true;
          cleanup();
          reject(error);
        }
      };

      // 1. Intentar usar WebSocket primero (más eficiente)
      try {
        const socket = (window as any).io?.();
        if (socket) {
          console.log('🔌 Using WebSocket for real-time updates');

          // Escuchar eventos específicos del trabajo
          socket.on(`job:${result.jobId}:progress`, (data: any) => {
            console.log('📡 WebSocket progress:', data);
            if (onProgress) onProgress(data.progress || 0, data.message || 'Processing...');
          });

          socket.on(`job:${result.jobId}:completed`, (data: any) => {
            console.log('✅ WebSocket job completed:', data);
            resolveOnce({
              success: true,
              downloadUrl: data.downloadUrl || result.downloadUrl,
              jobId: result.jobId
            });
          });

          socket.on(`job:${result.jobId}:failed`, (data: any) => {
            console.log('❌ WebSocket job failed:', data);
            rejectOnce(new Error(data.error || 'Export failed'));
          });

          // Timeout para WebSocket (fallback a polling si no hay respuesta en 3s)
          websocketTimeout = setTimeout(() => {
            console.log('⚠️ WebSocket timeout, falling back to polling');
            socket.off(`job:${result.jobId}:progress`);
            socket.off(`job:${result.jobId}:completed`);
            socket.off(`job:${result.jobId}:failed`);
            startPolling();
          }, 3000);

        } else {
          console.log('🔄 WebSocket not available, using polling');
          startPolling();
        }
      } catch (error) {
        console.log('🔄 WebSocket error, falling back to polling:', error);
        startPolling();
      }

      // 2. Función de fallback: Polling optimizado (menos frecuente)
      function startPolling() {
        if (isResolved) return;

        console.log('🔄 Starting optimized polling for:', result.jobId);

        let pollCount = 0;
        const maxPolls = 60; // Máximo 2 minutos de polling

        pollInterval = setInterval(async () => {
          if (isResolved) return;

          pollCount++;
          if (pollCount > maxPolls) {
            rejectOnce(new Error('Export timeout - job took too long'));
            return;
          }

          try {
            const statusResponse = await fetch(`${API_BASE_URL}${result.statusUrl}`);
            if (!statusResponse.ok) throw new Error('Failed to get job status');

            const statusData = await statusResponse.json();

            if (statusData.status === 'completed') {
              resolveOnce({
                success: true,
                downloadUrl: statusData.downloadUrl || result.downloadUrl,
                jobId: result.jobId
              });
            } else if (statusData.status === 'failed') {
              rejectOnce(new Error(statusData.error || 'Export failed'));
            } else {
              // En progreso
              const progress = statusData.progress || 0;
              if (onProgress) onProgress(progress, statusData.message || 'Procesando...');
            }
          } catch (error) {
            console.error('Error polling job status:', error);
            rejectOnce(new Error('Failed to get export status'));
          }
        }, 2000); // Poll cada 2 segundos (menos agresivo que antes)
      }
    });
  }

  // Procesamiento directo, devolver resultado tal cual
  return result;
};

// Función principal de exportación usando endpoint unificado
const exportAPI = async (
  format: string,
  payload: any,
  onProgress?: (progress: number, message: string) => void
): Promise<ExportResponse> => {
  // Usar siempre el endpoint unificado
  const endpoint = `/api/unified-export/${format}`;

  console.log(`🎬 Using unified export endpoint: ${endpoint}`);
  console.log(`🎬 Export payload:`, JSON.stringify(payload, null, 2));

  try {
    // Realizar petición
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(payload)
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error('❌ Export API error response:', errorData);
      throw new Error(errorData.error || `Export failed: ${response.statusText}`);
    }

    const result = await response.json();
    console.log('✅ Export API response:', result);

    // Manejar respuesta (con polling si es necesario)
    return handleExportResponse(result, onProgress);
  } catch (error) {
    console.error('💥 Export API error:', error);
    throw error;
  }
};

export interface ExportState {
  isExporting: boolean;
  progress: number;
  lastResult: string | null;
  error: string | null;
  currentStep?: string;
  isCompleted: boolean;
  downloadUrl?: string;
}

export interface ExportActions {
  exportSlideshow: () => Promise<void>;
  updateExportSettings: (updates: Partial<ExportSettings>) => void;
}

export interface UseExportManagementProps {
  timeline: TimelineItem[];
  images: ImageFile[];
  sessionId?: string;
  exportSettings: ExportSettings;
  updateExportState: (updates: Partial<ExportState>) => void;
  updateExportSettingsState: (updates: Partial<ExportSettings>) => void;
}

export const useExportManagement = ({
  timeline,
  images,
  sessionId,
  exportSettings,
  updateExportState,
  updateExportSettingsState
}: UseExportManagementProps): ExportActions => {

  const simulateProgress = useCallback(() => {
    const steps = [
      { progress: 10, message: 'Preparing images...' },
      { progress: 25, message: 'Processing transitions...' },
      { progress: 45, message: 'Encoding frames...' },
      { progress: 70, message: 'Optimizing output...' },
      { progress: 90, message: 'Finalizing export...' }
    ];

    let stepIndex = 0;
    const progressInterval = setInterval(() => {
      if (stepIndex < steps.length) {
        const step = steps[stepIndex];
        updateExportState({
          progress: step.progress,
          currentStep: step.message
        });
        stepIndex++;
      } else {
        clearInterval(progressInterval);
      }
    }, 800);

    return progressInterval;
  }, [updateExportState]);

  // NEW: Export using preview as base
  const exportFromPreview = useCallback(async (previewFilename: string) => {
    if (!previewFilename) {
      throw new Error('Preview filename is required');
    }

    updateExportState({
      isExporting: true,
      error: null,
      progress: 50,
      currentStep: `Converting preview to ${exportSettings.format.toUpperCase()}...`
    });

    try {
      const payload = {
        previewFilename,
        format: exportSettings.format,
        quality: exportSettings.quality,
        sessionId: sessionId
      };

      console.log('🔄 Export from preview payload:', payload);

      const response = await fetch(`${API_BASE_URL}/export/from-preview`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload)
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Export from preview failed');
      }

      const result = await response.json();
      console.log('✅ Export from preview result:', result);

      updateExportState({
        isExporting: false,
        progress: 100,
        currentStep: 'Export completed!',
        downloadUrl: result.downloadUrl,
        filename: result.filename
      });

      return result;
    } catch (error) {
      console.error('❌ Export from preview failed:', error);
      updateExportState({
        isExporting: false,
        error: error instanceof Error ? error.message : 'Export failed'
      });
      throw error;
    }
  }, [exportSettings, sessionId, updateExportState]);

  const exportSlideshow = useCallback(async () => {
    if (timeline.length === 0) return;

    updateExportState({
      isExporting: true,
      error: null,
      progress: 0,
      currentStep: 'Generating high-quality preview...'
    });

    try {
      // Step 1: Generate high-quality preview
      const previewPayload = {
        images: timeline.map(item => {
          const image = images.find(img => img.id === item.imageId);
          return { filename: image?.uploadedInfo?.filename || image?.name };
        }),
        transitions: timeline.slice(0, -1).map(item => ({
          type: item.transition?.type || 'cut',
          duration: item.transition?.duration || 0
        })),
        frameDurations: timeline.map(item => item.duration),
        sessionId: sessionId
      };

      console.log('🎬 Generating preview for export:', previewPayload);

      const previewResponse = await fetch(`${API_BASE_URL}/preview`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(previewPayload)
      });

      if (!previewResponse.ok) {
        const error = await previewResponse.json();
        throw new Error(error.error || 'Preview generation failed');
      }

      const previewResult = await previewResponse.json();
      console.log('✅ Preview generated:', previewResult);

      updateExportState({
        progress: 40,
        currentStep: 'Preview generated, converting to final format...'
      });

      // Step 2: Convert preview to desired format
      const exportResult = await exportFromPreview(previewResult.filename);

      return exportResult;
    } catch (error) {
      console.error('❌ Export slideshow failed:', error);
      updateExportState({
        isExporting: false,
        error: error instanceof Error ? error.message : 'Export failed'
      });
      throw error;
    }
  }, [timeline, images, sessionId, exportSettings, updateExportState, exportFromPreview]);

  // Legacy function - keeping for compatibility but not used in new flow
  const legacyExportSlideshow = useCallback(async () => {
    if (timeline.length === 0) return;

    updateExportState({
      isExporting: true,
      error: null,
      progress: 0
    });

    const progressInterval = simulateProgress();

    try {
      const payload = {
        images: timeline.map(item => {
          const image = images.find(img => img.id === item.imageId);
          return { filename: image?.uploadedInfo?.filename || image?.name };
        }),
        transitions: timeline.slice(0, -1).map(item => ({
          type: item.transition?.type || 'cut',
          duration: item.transition?.duration || 0
        })),
        frameDurations: timeline.map(item => item.duration),
        sessionId: sessionId,
        format: exportSettings.format,
        fps: exportSettings.fps,
        quality: exportSettings.quality,
        resolution: exportSettings.resolution?.preset || 'auto',
        videoConfig: exportSettings.resolution?.preset === 'custom' ? {
          resolution: {
            width: exportSettings.resolution?.width || 1920,
            height: exportSettings.resolution?.height || 1080
          }
        } : undefined
      };

      const format = exportSettings.format;
      const result = await exportAPI(format, payload);

      clearInterval(progressInterval);

      if (result.success) {
        // Para el sistema unificado, necesitamos hacer polling del job
        updateExportState({
          progress: 80,
          currentStep: 'Processing export...'
        });

        // Poll job status until completion
        const pollJobStatus = async (jobId: string, maxAttempts = 30) => {
          for (let i = 0; i < maxAttempts; i++) {
            try {
              const statusResponse = await fetch(`${API_BASE_URL}/api/export/status/${jobId}`);
              if (statusResponse.ok) {
                const statusData = await statusResponse.json();
                const job = statusData.job;
                
                updateExportState({
                  progress: 80 + (job.progress * 0.2), // Scale progress from 80-100%
                  currentStep: job.status === 'active' ? 'Processing export...' : 'Finalizing...'
                });
                
                if (job.status === 'completed') {
                  // Job completed, set completion state and trigger download
                  const downloadUrl = job.result?.downloadUrl || result.downloadUrl;

                  updateExportState({
                    isExporting: false,
                    progress: 100,
                    lastResult: downloadUrl,
                    currentStep: 'Complete!',
                    isCompleted: true,
                    downloadUrl: downloadUrl,
                    error: null
                  });

                  // Trigger automatic download
                  if (downloadUrl) {
                    triggerDownload(downloadUrl);
                  }
                  return;
                } else if (job.status === 'failed') {
                  throw new Error(`Export failed: ${job.failedReason || 'Unknown error'}`);
                }
              }
            } catch (error) {
              console.error('❌ Status check failed:', error);
            }
            
            // Wait 1 second before next poll
            await new Promise(resolve => setTimeout(resolve, 1000));
          }
          
          throw new Error('Export timed out - job did not complete in time');
        };

        // Handle response type - direct or job-based
        if (result.downloadUrl) {
          // Direct response from simple endpoints
          updateExportState({
            isExporting: false,
            progress: 100,
            lastResult: result.downloadUrl,
            currentStep: 'Complete!',
            isCompleted: true,
            downloadUrl: result.downloadUrl
          });
          console.log('✅ Direct export completed:', result.downloadUrl);

          // Trigger automatic download
          triggerDownload(result.downloadUrl);
        } else if (result.jobId) {
          // Job-based response - start polling
          await pollJobStatus(result.jobId);
        } else {
          throw new Error('Invalid response format - no downloadUrl or jobId received');
        }
      }
    } catch (error) {
      clearInterval(progressInterval);
      console.error('❌ Export failed:', error);
      updateExportState({
        isExporting: false,
        error: error instanceof Error ? error.message : 'Export failed',
        currentStep: undefined
      });
    }
  }, [timeline, images, sessionId, exportSettings, updateExportState, simulateProgress]);

  const updateExportSettings = useCallback((updates: Partial<ExportSettings>) => {
    updateExportSettingsState(updates);
  }, [updateExportSettingsState]);

  return {
    exportSlideshow,
    updateExportSettings
  };
};
